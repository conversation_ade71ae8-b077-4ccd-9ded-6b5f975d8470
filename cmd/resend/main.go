package main

import (
	"context"
	"crypto/ecdsa"
	"encoding/json"
	"flag"
	"fmt"
	"math/big"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"

	common2 "gitlab.jcwork.net/assets-management/sipanzi/common"
	"gitlab.jcwork.net/assets-management/sipanzi/config"
	"gitlab.jcwork.net/assets-management/sipanzi/consts"
	"gitlab.jcwork.net/assets-management/sipanzi/contracts/batchtransfer"
	myprogram "gitlab.jcwork.net/assets-management/sipanzi/contracts/program"
	"gitlab.jcwork.net/assets-management/sipanzi/db"
	"gitlab.jcwork.net/assets-management/sipanzi/lark"
	"gitlab.jcwork.net/assets-management/sipanzi/log"
)

type ResendContext struct {
	ctx           context.Context
	polCli        *ethclient.Client
	juCli         *ethclient.Client
	mpg           *myprogram.Program
	batchTransfer *batchtransfer.BatchTransfer
	lark          *lark.Lark
	pk            *ecdsa.PrivateKey
	signerFn      bind.SignerFn
	address       common.Address
	tableId       string
	batchCount    int
}

func main() {
	env := os.Getenv("ENV")
	if env == "" {
		env = consts.EnvLocal
		_ = os.Setenv("ENV", env)
	}
	log.InitLogger(env)

	log.Logger.Info("resend tool started", zap.String("env", env))

	var (
		confFlag    = flag.String("conf", "config/config.yaml", "configuration file path")
		fromHeight  = flag.Uint64("from", 0, "start block height")
		toHeight    = flag.Uint64("to", 0, "end block height")
		batchSize   = flag.Int("batch", 50, "batch size for processing")
	)
	flag.Parse()

	if *fromHeight == 0 || *toHeight == 0 {
		log.Logger.Fatal("from and to block heights are required")
	}

	if *fromHeight > *toHeight {
		log.Logger.Fatal("from height cannot be greater than to height")
	}

	cfg := config.LoadConfig(env, *confFlag)
	db.InitDb(cfg.Db)

	resendCtx, err := NewResendContext(cfg, *batchSize)
	if err != nil {
		log.Logger.Fatal("failed to create resend context", zap.Error(err))
	}

	log.Logger.Info("starting resend process",
		zap.Uint64("from", *fromHeight),
		zap.Uint64("to", *toHeight),
		zap.Int("batch_size", *batchSize))

	err = resendCtx.ProcessMissedTransactions(*fromHeight, *toHeight)
	if err != nil {
		log.Logger.Fatal("failed to process missed transactions", zap.Error(err))
	}

	log.Logger.Info("resend process completed successfully")
}

func NewResendContext(cfg config.Config, batchCount int) (*ResendContext, error) {
	ctx := context.Background()

	polCli, err := ethclient.Dial(cfg.Chain.PolygonRpcUrl)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to polygon client: %w", err)
	}

	juCli, err := ethclient.Dial(cfg.Chain.JuRpcUrl)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ju client: %w", err)
	}

	larkCli := lark.NewLark(cfg.Lark)

	batchTransfer, err := batchtransfer.NewBatchTransfer(common.HexToAddress(consts.TransferBatchContract), juCli)
	if err != nil {
		return nil, fmt.Errorf("failed to create batch transfer contract: %w", err)
	}

	mpg, err := myprogram.NewProgram(common.Address{}, polCli)
	if err != nil {
		return nil, fmt.Errorf("failed to create program contract: %w", err)
	}

	pk := common2.GetPK(cfg.Chain.Seed)
	auth, err := bind.NewKeyedTransactorWithChainID(pk, big.NewInt(210000))
	if err != nil {
		return nil, fmt.Errorf("failed to create transactor: %w", err)
	}

	// 获取表格ID
	tableId, err := larkCli.LastTableId()
	if err != nil {
		return nil, fmt.Errorf("failed to get table id: %w", err)
	}

	return &ResendContext{
		ctx:           ctx,
		polCli:        polCli,
		juCli:         juCli,
		mpg:           mpg,
		batchTransfer: batchTransfer,
		lark:          larkCli,
		pk:            pk,
		signerFn:      auth.Signer,
		address:       crypto.PubkeyToAddress(pk.PublicKey),
		tableId:       tableId,
		batchCount:    batchCount,
	}, nil
}

func (r *ResendContext) ProcessMissedTransactions(fromHeight, toHeight uint64) error {
	log.Logger.Info("processing missed transactions",
		zap.Uint64("from", fromHeight),
		zap.Uint64("to", toHeight))

	var allMissedRecords []common2.Record
	maxBlockStep := uint64(100) // 每次处理100个区块

	for currentFrom := fromHeight; currentFrom <= toHeight; {
		currentTo := currentFrom + maxBlockStep - 1
		if currentTo > toHeight {
			currentTo = toHeight
		}

		log.Logger.Info("scanning blocks",
			zap.Uint64("from", currentFrom),
			zap.Uint64("to", currentTo))

		missedRecords, err := r.scanBlocksForMissedTransactions(currentFrom, currentTo)
		if err != nil {
			return fmt.Errorf("failed to scan blocks %d-%d: %w", currentFrom, currentTo, err)
		}

		allMissedRecords = append(allMissedRecords, missedRecords...)

		log.Logger.Info("found missed transactions in range",
			zap.Uint64("from", currentFrom),
			zap.Uint64("to", currentTo),
			zap.Int("count", len(missedRecords)))

		currentFrom = currentTo + 1
	}

	if len(allMissedRecords) == 0 {
		log.Logger.Info("no missed transactions found")
		return nil
	}

	log.Logger.Info("total missed transactions found", zap.Int("count", len(allMissedRecords)))

	// 批量处理遗漏的交易
	return r.processMissedRecordsInBatches(allMissedRecords)
}
func (r *ResendContext) scanBlocksForMissedTransactions(fromHeight, toHeight uint64) ([]common2.Record, error) {
	// 获取汇率
	juAp, polBp, err := r.getQuote()
	if err != nil {
		return nil, fmt.Errorf("failed to get quote: %w", err)
	}
	rate := polBp / juAp * (1 - consts.ADDITION)

	// 扫描区块日志
	logs, err := r.polCli.FilterLogs(r.ctx, ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromHeight)),
		ToBlock:   big.NewInt(int64(toHeight)),
		Addresses: []common.Address{common.HexToAddress("******************************************")},
		Topics: [][]common.Hash{
			{
				common.HexToHash(consts.TopicRewardWithdraw),
			},
		},
	})

	if err != nil {
		return nil, fmt.Errorf("failed to filter logs: %w", err)
	}

	var missedRecords []common2.Record

	for _, l := range logs {
		if l.Topics[0].String() == consts.TopicRewardWithdraw {
			// 检查数据库中是否已存在该交易
			exists, err := r.isTransactionProcessed(l.TxHash.Hex())
			if err != nil {
				log.Logger.Error("failed to check if transaction is processed",
					zap.String("txHash", l.TxHash.Hex()),
					zap.Error(err))
				continue
			}

			if exists {
				log.Logger.Debug("transaction already processed, skipping",
					zap.String("txHash", l.TxHash.Hex()))
				continue
			}

			// 解析事件
			rewardWithdrawLog, err := r.mpg.ParseRewardWithdraw(l)
			if err != nil {
				log.Logger.Error("failed to parse reward withdraw log",
					zap.String("txHash", l.TxHash.Hex()),
					zap.Error(err))
				continue
			}

			if rewardWithdrawLog.ToJUAmount.Cmp(big.NewInt(0)) == 0 {
				continue
			}

			polAmountD := decimal.NewFromBigInt(rewardWithdrawLog.ToJUAmount, 0-18)
			juAmount := polAmountD.Mul(decimal.NewFromFloat(rate))

			record := common2.Record{
				PolHash:          l.TxHash,
				From:             rewardWithdrawLog.From,
				PolAmount:        polAmountD,
				JuAp:             juAp,
				PolAp:            polBp,
				Addition:         consts.ADDITION,
				Rate:             rate,
				JuAmount:         juAmount,
				TransferJuAmount: juAmount,
			}

			missedRecords = append(missedRecords, record)
			log.Logger.Info("found missed transaction",
				zap.String("txHash", l.TxHash.Hex()),
				zap.String("from", rewardWithdrawLog.From.Hex()),
				zap.String("amount", juAmount.String()))
		}
	}

	return missedRecords, nil
}

func (r *ResendContext) isTransactionProcessed(polHash string) (bool, error) {
	var count int64
	err := db.GetDb().Model(&db.Record{}).Where("pol_hash = ?", polHash).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
func (r *ResendContext) processMissedRecordsInBatches(records []common2.Record) error {
	log.Logger.Info("processing missed records in batches",
		zap.Int("total_records", len(records)),
		zap.Int("batch_size", r.batchCount))

	for i := 0; i < len(records); i += r.batchCount {
		end := i + r.batchCount
		if end > len(records) {
			end = len(records)
		}

		batch := records[i:end]
		log.Logger.Info("processing batch",
			zap.Int("batch_start", i+1),
			zap.Int("batch_end", end),
			zap.Int("batch_size", len(batch)))

		err := r.processBatch(batch)
		if err != nil {
			return fmt.Errorf("failed to process batch %d-%d: %w", i+1, end, err)
		}

		log.Logger.Info("batch processed successfully",
			zap.Int("batch_start", i+1),
			zap.Int("batch_end", end))

		// 添加延迟以避免过于频繁的交易
		if end < len(records) {
			time.Sleep(time.Second * 2)
		}
	}

	return nil
}

func (r *ResendContext) processBatch(records []common2.Record) error {
	// 执行批量转账
	txHash, err := r.transferBatch(records)
	if err != nil {
		return fmt.Errorf("failed to transfer batch: %w", err)
	}

	// 等待交易确认并获取收据
	receipt, err := r.getReceipt(txHash)
	if err != nil {
		return fmt.Errorf("failed to get receipt: %w", err)
	}

	gasUsed := decimal.NewFromBigInt(new(big.Int).SetUint64(receipt.GasUsed), 0-18)
	gasPerTransfer := gasUsed.Div(decimal.NewFromInt(int64(len(records))))

	log.Logger.Info("batch transfer completed",
		zap.String("txHash", "https://explorer.juscan.io/tx/"+txHash.String()),
		zap.Int("count", len(records)),
		zap.String("gas_used", gasUsed.String()),
		zap.String("gas_per_transfer", gasPerTransfer.String()))

	// 记录到 Lark
	err = r.batchRecord(records, txHash.String(), gasPerTransfer)
	if err != nil {
		log.Logger.Error("failed to record to lark", zap.Error(err))
		// 不返回错误，因为转账已经成功
	}

	// 保存到数据库
	err = r.batchSave(records, txHash.String(), gasPerTransfer)
	if err != nil {
		return fmt.Errorf("failed to save to database: %w", err)
	}

	return nil
}

func (r *ResendContext) transferBatch(records []common2.Record) (common.Hash, error) {
	var addresses []common.Address
	totalAmount := big.NewInt(0)
	var amounts []*big.Int

	// 提取所有记录中的地址和金额
	for _, record := range records {
		amount := record.TransferJuAmount.Mul(decimal.NewFromInt(1e18)).BigInt()
		amounts = append(amounts, amount)
		addresses = append(addresses, record.From)
		totalAmount.Add(totalAmount, amount)
	}

	nonce, err := r.juCli.PendingNonceAt(r.ctx, r.address)
	if err != nil {
		return common.Hash{}, fmt.Errorf("failed to get nonce: %w", err)
	}

	// 调用批量转账合约
	tx, err := r.batchTransfer.BatchTransferJU(&bind.TransactOpts{
		From:   r.address,
		Nonce:  big.NewInt(int64(nonce)),
		Signer: r.signerFn,
		Value:  totalAmount,
	}, addresses, amounts)

	if err != nil {
		return common.Hash{}, fmt.Errorf("batch transfer failed: %w", err)
	}

	log.Logger.Info("batch transfer submitted",
		zap.String("txHash", tx.Hash().Hex()),
		zap.Int("count", len(addresses)),
		zap.String("totalAmount", totalAmount.String()))

	return tx.Hash(), nil
}
func (r *ResendContext) getReceipt(txHash common.Hash) (*types.Receipt, error) {
	for i := 0; i < 60; i++ { // 最多等待60秒
		receipt, err := r.juCli.TransactionReceipt(r.ctx, txHash)
		if err == nil {
			return receipt, nil
		}

		log.Logger.Debug("waiting for transaction receipt",
			zap.String("txHash", txHash.Hex()),
			zap.Int("attempt", i+1))
		time.Sleep(time.Second)
	}

	return nil, fmt.Errorf("transaction receipt not found after 60 seconds")
}

func (r *ResendContext) batchRecord(records []common2.Record, juHash string, gasUsed decimal.Decimal) error {
	var data []map[string]interface{}

	for _, record := range records {
		fields := map[string]interface{}{
			"Pol hash":   record.PolHash.String(),
			"充币地址":       record.From,
			"充币数量":       record.PolAmount.String(),
			"Juusdt bp":  decimal.NewFromFloat(record.JuAp).String(),
			"Polusdt ap": decimal.NewFromFloat(record.PolAp).String(),
			"汇率加点":       decimal.NewFromFloat(record.Addition).String(),
			"实际汇率":       decimal.NewFromFloat(record.Rate),
			"ju 数量":      record.JuAmount.String(),
			"转账数量":       record.TransferJuAmount.String(),
			"转账时间":       time.Now().UnixMilli(),
			"转账地址":       record.From.String(),
			"是否转账":       "是",
			"Gas":        gasUsed,
			"转账 hash":    juHash,
		}
		data = append(data, fields)
	}

	return r.lark.BatchWriteToBitable(r.tableId, data)
}

func (r *ResendContext) batchSave(records []common2.Record, juHash string, gasUsed decimal.Decimal) error {
	var data []*db.Record

	for _, record := range records {
		r := &db.Record{
			PolHash:          record.PolHash.Hex(),
			From:             record.From.Hex(),
			PolAmount:        record.PolAmount.String(),
			JuAp:             record.JuAp,
			PolAp:            record.PolAp,
			Addition:         record.Addition,
			Rate:             record.Rate,
			JuAmount:         record.JuAmount.String(),
			TransferJuAmount: record.TransferJuAmount.String(),
			ToAddress:        record.From.Hex(),
			TransferTime:     time.Now().Unix(),
			Gas:              gasUsed.String(),
			JuHash:           juHash,
		}
		data = append(data, r)
	}

	return db.GetDb().Clauses(clause.Insert{Modifier: "IGNORE"}).Create(&data).Error
}

func (r *ResendContext) getQuote() (float64, float64, error) {
	resp, err := http.Get("https://api.jucoin.com/v1/spot/public/ticker/book?symbols=ju_usdt,pol_usdt")
	if err != nil {
		return 0, 0, err
	}
	defer resp.Body.Close()

	type TickerBook struct {
		Biz     string        `json:"biz"`
		Code    int           `json:"code"`
		Msg     string        `json:"msg"`
		MsgInfo []interface{} `json:"msgInfo"`
		Data    []struct {
			S  string `json:"s"`
			T  int64  `json:"t"`
			Ap string `json:"ap"`
			Aq string `json:"aq"`
			Bp string `json:"bp"`
			Bq string `json:"bq"`
		} `json:"data"`
	}

	var tickerBook TickerBook
	err = json.NewDecoder(resp.Body).Decode(&tickerBook)
	if err != nil {
		return 0, 0, err
	}

	ju, err := strconv.ParseFloat(tickerBook.Data[0].Ap, 64)
	if err != nil {
		return 0, 0, err
	}

	poly, err := strconv.ParseFloat(tickerBook.Data[1].Bp, 64)
	if err != nil {
		return 0, 0, err
	}

	return ju, poly, nil
}
